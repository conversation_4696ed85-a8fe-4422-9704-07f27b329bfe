{% extends 'base.html' %}

{% block page_title %}QR Code Scanner{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto" x-data="qrScanner()">
    <!-- Scanner Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-2xl font-bold text-gray-900">QR Code Scanner</h1>
            <div class="flex space-x-2">
                <button @click="toggleCamera()" 
                        :class="cameraActive ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'"
                        class="px-4 py-2 text-white rounded-lg transition-colors">
                    <span x-text="cameraActive ? 'Stop Camera' : 'Start Camera'"></span>
                </button>
                <button @click="showHistory = !showHistory" 
                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                    <span x-text="showHistory ? 'Hide History' : 'Show History'"></span>
                </button>
            </div>
        </div>
        
        <!-- Camera Status -->
        <div x-show="cameraError" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-red-800" x-text="cameraError"></p>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Camera Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Camera Scanner</h2>
            
            <!-- Video Element -->
            <div class="relative mb-4">
                <video id="qr-video" 
                       class="w-full max-w-md mx-auto rounded-lg bg-gray-100"
                       x-show="cameraActive"
                       autoplay 
                       playsinline>
                </video>
                
                <!-- Camera Overlay -->
                <div x-show="cameraActive" class="qr-scanner-overlay absolute inset-0 pointer-events-none">
                    <!-- Scanning indicator -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="w-48 h-48 border-2 border-green-500 rounded-lg animate-pulse"></div>
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-sm font-medium bg-black bg-opacity-50 px-2 py-1 rounded mt-28">
                            Position QR code within frame
                        </div>
                    </div>
                    
                    <!-- Corner indicators -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="w-48 h-48 relative">
                            <!-- Top-left corner -->
                            <div class="absolute top-0 left-0 w-6 h-6 border-l-4 border-t-4 border-green-500"></div>
                            <!-- Top-right corner -->
                            <div class="absolute top-0 right-0 w-6 h-6 border-r-4 border-t-4 border-green-500"></div>
                            <!-- Bottom-left corner -->
                            <div class="absolute bottom-0 left-0 w-6 h-6 border-l-4 border-b-4 border-green-500"></div>
                            <!-- Bottom-right corner -->
                            <div class="absolute bottom-0 right-0 w-6 h-6 border-r-4 border-b-4 border-green-500"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Placeholder when camera is off -->
                <div x-show="!cameraActive" class="w-full max-w-md mx-auto h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <p>Click "Start Camera" to begin scanning</p>
                    </div>
                </div>
            </div>
            
            <!-- Camera Controls -->
            <div x-show="cameraActive" class="flex justify-center space-x-4 mb-4">
                <button @click="switchCamera()" 
                        class="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Switch Camera
                </button>
                <button @click="toggleFlashlight()" 
                        :class="flashlightOn ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-gray-500 hover:bg-gray-600'"
                        class="px-3 py-2 text-white rounded-lg transition-colors text-sm">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    <span x-text="flashlightOn ? 'Flash On' : 'Flash Off'"></span>
                </button>
            </div>
            
            <!-- Manual QR Code Input -->
            <div class="border-t pt-4">
                <label for="manual-qr" class="block text-sm font-medium text-gray-700 mb-2">
                    Or enter QR code manually:
                </label>
                <div class="flex space-x-2">
                    <input type="text" 
                           id="manual-qr" 
                           x-model="manualQrCode"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter QR code data"
                           @keyup.enter="processManualQr()">
                    <button @click="processManualQr()" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        Process
                    </button>
                </div>
            </div>
        </div>

        <!-- Scan Actions Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Scan Actions</h2>
            
            <!-- Scan Type Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Scan Type:</label>
                <select x-model="scanType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="inventory_check">Inventory Check</option>
                    <option value="issuance">Issuance</option>
                    <option value="return">Return</option>
                </select>
            </div>
            
            <!-- Location Input -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Location:</label>
                <input type="text" 
                       x-model="location"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter location (optional)">
            </div>
            
            <!-- Notes Input -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Notes:</label>
                <textarea x-model="notes" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter notes (optional)"></textarea>
            </div>
            
            <!-- Pending Requests (for issuance) -->
            <div x-show="scanType === 'issuance'" class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Request Item:</label>
                <select x-model="selectedRequestItem" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select a request item...</option>
                    <template x-for="request in pendingRequests" :key="request.id">
                        <optgroup :label="request.request_number + ' - ' + request.requester">
                            <template x-for="item in request.items" :key="item.id">
                                <option :value="item.id" x-text="item.supply_item_name + ' (Remaining: ' + item.remaining + ')'"></option>
                            </template>
                        </optgroup>
                    </template>
                </select>
            </div>
            
            <!-- Return Quantity (for returns) -->
            <div x-show="scanType === 'return'" class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Return Quantity:</label>
                <input type="number" 
                       x-model="returnQuantity"
                       min="1"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter quantity to return">
            </div>
        </div>
    </div>

    <!-- Scan Results -->
    <div x-show="lastScanResult" class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Last Scan Result</h2>
        <div x-show="lastScanResult.success" class="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-green-800 font-medium" x-text="lastScanResult.message"></span>
            </div>
            <div x-show="lastScanResult.data" class="mt-3 text-sm text-green-700">
                <p><strong>Item:</strong> <span x-text="lastScanResult.data?.supply_item?.name"></span></p>
                <p><strong>Current Stock:</strong> <span x-text="lastScanResult.data?.supply_item?.current_stock"></span> <span x-text="lastScanResult.data?.supply_item?.unit_of_measure"></span></p>
                <p><strong>Scan Time:</strong> <span x-text="formatDateTime(lastScanResult.data?.scan_log?.scan_datetime)"></span></p>
            </div>
        </div>
        <div x-show="!lastScanResult.success" class="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-red-800 font-medium" x-text="lastScanResult.message"></span>
            </div>
        </div>
    </div>

    <!-- Scan History -->
    <div x-show="showHistory" class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">Recent Scans</h2>
            <a href="{% url 'scan_history' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View All History →
            </a>
        </div>
        <div id="recent-scans" hx-get="{% url 'scan_history' %}?limit=10" hx-trigger="load" hx-target="#recent-scans">
            <div class="text-center py-4 text-gray-500">Loading recent scans...</div>
        </div>
    </div>
</div>

<!-- CSRF Token -->
{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
function qrScanner() {
    return {
        cameraActive: false,
        cameraError: '',
        video: null,
        canvas: null,
        context: null,
        scanType: 'inventory_check',
        location: '',
        notes: '',
        manualQrCode: '',
        lastScanResult: null,
        showHistory: false,
        pendingRequests: [],
        selectedRequestItem: '',
        returnQuantity: 1,
        
        init() {
            this.canvas = document.createElement('canvas');
            this.context = this.canvas.getContext('2d');
            this.loadPendingRequests();
        },
        
        async toggleCamera() {
            if (this.cameraActive) {
                this.stopCamera();
            } else {
                await this.startCamera();
            }
        },
        
        async startCamera() {
            try {
                this.cameraError = '';
                this.video = document.getElementById('qr-video');
                
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera access not supported by this browser');
                }
                
                // Try different camera configurations for better compatibility
                let stream;
                try {
                    // First try with back camera (environment)
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: { 
                            facingMode: { exact: 'environment' },
                            width: { ideal: 1280, min: 640 },
                            height: { ideal: 720, min: 480 }
                        }
                    });
                } catch (backCameraError) {
                    console.log('Back camera not available, trying front camera');
                    // Fallback to any available camera
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: { 
                            width: { ideal: 1280, min: 640 },
                            height: { ideal: 720, min: 480 }
                        }
                    });
                }
                
                this.video.srcObject = stream;
                this.cameraActive = true;
                
                // Start scanning when video is ready
                this.video.addEventListener('loadedmetadata', () => {
                    this.canvas.width = this.video.videoWidth;
                    this.canvas.height = this.video.videoHeight;
                    this.scanQRCode();
                });
                
                // Handle video errors
                this.video.addEventListener('error', (e) => {
                    console.error('Video error:', e);
                    this.cameraError = 'Video playback error occurred';
                    this.stopCamera();
                });
                
            } catch (error) {
                console.error('Camera error:', error);
                let errorMessage = 'Unable to access camera. ';
                
                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please allow camera permissions and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Camera not supported by this browser.';
                } else {
                    errorMessage += error.message || 'Please check your camera settings.';
                }
                
                this.cameraError = errorMessage;
                this.cameraActive = false;
            }
        },
        
        stopCamera() {
            if (this.video && this.video.srcObject) {
                const tracks = this.video.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                this.video.srcObject = null;
            }
            this.cameraActive = false;
        },
        
        scanQRCode() {
            if (!this.cameraActive || !this.video) return;
            
            if (this.video.readyState === this.video.HAVE_ENOUGH_DATA) {
                try {
                    // Draw video frame to canvas
                    this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
                    const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
                    
                    // Scan for QR code with improved options
                    const code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "dontInvert",
                    });
                    
                    if (code && code.data) {
                        console.log('QR Code detected:', code.data);
                        
                        // Visual feedback for successful scan
                        this.showScanSuccess();
                        
                        // Process the QR code
                        this.processQRCode(code.data);
                        return; // Stop scanning after successful detection
                    }
                } catch (error) {
                    console.error('QR scanning error:', error);
                }
            }
            
            // Continue scanning at 30 FPS for better performance
            setTimeout(() => {
                if (this.cameraActive) {
                    requestAnimationFrame(() => this.scanQRCode());
                }
            }, 33); // ~30 FPS
        },
        
        showScanSuccess() {
            // Flash green overlay to indicate successful scan
            const overlay = document.querySelector('.qr-scanner-overlay');
            if (overlay) {
                overlay.style.backgroundColor = 'rgba(16, 185, 129, 0.3)';
                setTimeout(() => {
                    overlay.style.backgroundColor = 'transparent';
                }, 500);
            }
        },
        
        async processQRCode(qrData) {
            try {
                // Validate QR data format
                if (!qrData || qrData.trim().length === 0) {
                    this.lastScanResult = {
                        success: false,
                        message: 'Invalid QR code data'
                    };
                    return;
                }
                
                // Validate scan type specific requirements
                if (this.scanType === 'issuance' && !this.selectedRequestItem) {
                    this.lastScanResult = {
                        success: false,
                        message: 'Please select a request item for issuance'
                    };
                    return;
                }
                
                if (this.scanType === 'return' && (!this.returnQuantity || this.returnQuantity < 1)) {
                    this.lastScanResult = {
                        success: false,
                        message: 'Please enter a valid return quantity'
                    };
                    return;
                }
                
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                
                const payload = {
                    qr_code_data: qrData.trim(),
                    scan_type: this.scanType,
                    location: this.location.trim(),
                    notes: this.notes.trim()
                };
                
                if (this.scanType === 'issuance' && this.selectedRequestItem) {
                    payload.request_item_id = parseInt(this.selectedRequestItem);
                }
                
                if (this.scanType === 'return' && this.returnQuantity) {
                    payload.quantity = parseInt(this.returnQuantity);
                }
                
                // Show processing state
                this.lastScanResult = {
                    success: null,
                    message: 'Processing scan...'
                };
                
                const response = await fetch('{% url "process_qr_scan" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify(payload)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                this.lastScanResult = result;
                
                if (result.success) {
                    // Play success sound (if available)
                    this.playSuccessSound();
                    
                    // Clear form fields
                    this.location = '';
                    this.notes = '';
                    if (this.scanType !== 'issuance') {
                        this.selectedRequestItem = '';
                    }
                    this.returnQuantity = 1;
                    
                    // Refresh pending requests if it was an issuance
                    if (this.scanType === 'issuance') {
                        this.loadPendingRequests();
                    }
                    
                    // Refresh recent scans if history is shown
                    if (this.showHistory) {
                        htmx.trigger('#recent-scans', 'load');
                    }
                    
                    // Auto-hide success message after 5 seconds
                    setTimeout(() => {
                        if (this.lastScanResult && this.lastScanResult.success) {
                            this.lastScanResult = null;
                        }
                    }, 5000);
                } else {
                    // Play error sound (if available)
                    this.playErrorSound();
                }
                
            } catch (error) {
                console.error('Scan processing error:', error);
                this.lastScanResult = {
                    success: false,
                    message: 'Network error. Please check your connection and try again.'
                };
                this.playErrorSound();
            }
        },
        
        playSuccessSound() {
            // Create a simple success beep
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (error) {
                // Audio not supported, ignore
            }
        },
        
        playErrorSound() {
            // Create a simple error beep
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 300;
                oscillator.type = 'sawtooth';
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (error) {
                // Audio not supported, ignore
            }
        },
        
        processManualQr() {
            if (this.manualQrCode.trim()) {
                this.processQRCode(this.manualQrCode.trim());
                this.manualQrCode = '';
            }
        },
        
        async loadPendingRequests() {
            try {
                const response = await fetch('{% url "pending_requests_for_scan" %}');
                const result = await response.json();
                
                if (result.success) {
                    this.pendingRequests = result.data;
                }
            } catch (error) {
                console.error('Error loading pending requests:', error);
            }
        },
        
        formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString();
        }
    }
}
</script>
{% endblock %}