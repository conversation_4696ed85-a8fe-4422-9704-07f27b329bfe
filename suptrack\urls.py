from django.urls import path
from .views import home
from .auth_views import (
    custom_login, custom_register, custom_logout, 
    dashboard, admin_dashboard, gso_dashboard,
    dashboard_stats_htmx, dashboard_activities_htmx, dashboard_widgets_htmx
)
from .request_views import (
    create_request, request_list, request_detail, edit_request, delete_request,
    request_status_update_htmx, my_requests_htmx
)
from .approval_views import (
    pending_requests_view, approve_request_htmx, reject_request_htmx,
    bulk_approve_requests_htmx, rejection_modal_htmx, request_detail_modal_htmx,
    pending_requests_table_htmx, approval_history_view
)
from .qr_views import (
    qr_scanner, process_qr_scan, scan_history, get_supply_item_info,
    pending_requests_for_scan, qr_code_management, generate_qr_code,
    regenerate_qr_code, download_qr_code, batch_generate_qr_codes,
    qr_code_list, qr_code_preview
)
from .inventory_views import (
    inventory_list, inventory_list_htmx, add_supply_item, edit_supply_item,
    supply_item_detail, delete_supply_item, delete_confirmation_modal,
    stock_adjustment, low_stock_alerts, inventory_transactions
)

urlpatterns = [
    path('', home, name='home'),
    
    # Authentication URLs
    path('login/', custom_login, name='login'),
    path('register/', custom_register, name='register'),
    path('logout/', custom_logout, name='logout'),
    
    # Dashboard URLs
    path('dashboard/', dashboard, name='dashboard'),
    path('admin-dashboard/', admin_dashboard, name='admin_dashboard'),
    path('gso-dashboard/', gso_dashboard, name='gso_dashboard'),
    
    # Supply Request URLs
    path('requests/', request_list, name='request_list'),
    path('requests/create/', create_request, name='create_request'),
    path('requests/<int:pk>/', request_detail, name='request_detail'),
    path('requests/<int:pk>/edit/', edit_request, name='edit_request'),
    path('requests/<int:pk>/delete/', delete_request, name='delete_request'),
    
    # Approval URLs
    path('approvals/', pending_requests_view, name='pending_requests_view'),
    path('approvals/history/', approval_history_view, name='approval_history_view'),
    
    # HTMX endpoints for real-time updates
    path('htmx/dashboard-stats/', dashboard_stats_htmx, name='dashboard_stats_htmx'),
    path('htmx/dashboard-activities/', dashboard_activities_htmx, name='dashboard_activities_htmx'),
    path('htmx/dashboard-widgets/', dashboard_widgets_htmx, name='dashboard_widgets_htmx'),
    path('htmx/request-status/<int:pk>/', request_status_update_htmx, name='request_status_update_htmx'),
    path('htmx/my-requests/', my_requests_htmx, name='my_requests_htmx'),
    
    # Approval HTMX endpoints
    path('htmx/approve-request/<int:request_id>/', approve_request_htmx, name='approve_request_htmx'),
    path('htmx/reject-request/<int:request_id>/', reject_request_htmx, name='reject_request_htmx'),
    path('htmx/bulk-approve/', bulk_approve_requests_htmx, name='bulk_approve_requests_htmx'),
    path('htmx/rejection-modal/<int:request_id>/', rejection_modal_htmx, name='rejection_modal_htmx'),
    path('htmx/request-detail-modal/<int:request_id>/', request_detail_modal_htmx, name='request_detail_modal_htmx'),
    path('htmx/pending-requests-table/', pending_requests_table_htmx, name='pending_requests_table_htmx'),
    
    # QR Code Scanner URLs
    path('qr-scanner/', qr_scanner, name='qr_scanner'),
    path('scan-history/', scan_history, name='scan_history'),
    path('api/process-qr-scan/', process_qr_scan, name='process_qr_scan'),
    path('api/supply-item/<str:qr_code_data>/', get_supply_item_info, name='get_supply_item_info'),
    path('api/pending-requests-for-scan/', pending_requests_for_scan, name='pending_requests_for_scan'),
    
    # QR Code Management URLs
    path('qr-management/', qr_code_management, name='qr_code_management'),
    path('qr-codes/', qr_code_list, name='qr_code_list'),
    path('qr-codes/generate/<int:item_id>/', generate_qr_code, name='generate_qr_code'),
    path('qr-codes/regenerate/<int:item_id>/', regenerate_qr_code, name='regenerate_qr_code'),
    path('qr-codes/download/<int:item_id>/', download_qr_code, name='download_qr_code'),
    path('qr-codes/preview/<int:item_id>/', qr_code_preview, name='qr_code_preview'),
    path('api/batch-generate-qr-codes/', batch_generate_qr_codes, name='batch_generate_qr_codes'),
    
    # Inventory Management URLs
    path('inventory/', inventory_list, name='inventory_list'),
    path('inventory/add/', add_supply_item, name='add_supply_item'),
    path('inventory/<int:pk>/', supply_item_detail, name='supply_item_detail'),
    path('inventory/<int:pk>/edit/', edit_supply_item, name='edit_supply_item'),
    path('inventory/<int:pk>/delete/', delete_supply_item, name='delete_supply_item'),
    path('inventory/<int:pk>/adjust-stock/', stock_adjustment, name='stock_adjustment'),
    path('inventory/low-stock/', low_stock_alerts, name='low_stock_alerts'),
    path('inventory/transactions/', inventory_transactions, name='inventory_transactions'),
    
    # Inventory HTMX endpoints
    path('htmx/inventory-list/', inventory_list_htmx, name='inventory_list_htmx'),
    path('htmx/delete-confirmation/<int:pk>/', delete_confirmation_modal, name='delete_confirmation_modal'),
]
