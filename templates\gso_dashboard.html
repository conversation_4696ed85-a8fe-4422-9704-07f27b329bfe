{% extends 'base.html' %}

{% block title %}GSO Staff Dashboard - Smart Supply Management{% endblock %}

{% block page_title %}GSO Staff Dashboard{% endblock %}

{% block content %}
<!-- Dashboard Statistics with HTMX auto-refresh -->
<div id="dashboard-stats" 
     hx-get="{% url 'dashboard_stats_htmx' %}" 
     hx-trigger="load, every 30s"
     hx-swap="innerHTML"
     class="mb-8">
    {% include 'partials/dashboard_stats.html' %}
</div>

<!-- GSO Quick Actions with HTMX -->
<div id="dashboard-widgets" 
     hx-get="{% url 'dashboard_widgets_htmx' %}" 
     hx-trigger="load, every 60s"
     hx-swap="innerHTML"
     class="mb-8">
    {% include 'partials/dashboard_widgets.html' %}
</div>

<!-- Pending Approvals with HTMX auto-refresh -->
<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Pending Approvals</h3>
            <div class="flex items-center space-x-2">
                <a href="{% url 'pending_requests_view' %}" 
                   class="text-sm text-blue-600 hover:text-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    View All
                </a>
                <button hx-get="{% url 'dashboard_activities_htmx' %}" 
                        hx-target="#dashboard-activities"
                        hx-swap="innerHTML"
                        class="text-sm text-blue-600 hover:text-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
        <div id="dashboard-activities" 
             hx-get="{% url 'dashboard_activities_htmx' %}" 
             hx-trigger="load, every 30s"
             hx-swap="innerHTML">
            {% include 'partials/dashboard_activities.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // GSO Dashboard-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Add loading states for HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.id === 'dashboard-stats' || target.id === 'dashboard-widgets' || target.id === 'dashboard-activities') {
                target.style.opacity = '0.7';
            }
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.id === 'dashboard-stats' || target.id === 'dashboard-widgets' || target.id === 'dashboard-activities') {
                target.style.opacity = '1';
            }
        });
        

        
        // GSO-specific: More frequent updates for pending approvals
        setInterval(function() {
            const activitiesElement = document.getElementById('dashboard-activities');
            if (activitiesElement) {
                htmx.trigger(activitiesElement, 'refresh');
            }
        }, 20000); // Refresh every 20 seconds for GSO staff
    });
</script>
{% endblock %}